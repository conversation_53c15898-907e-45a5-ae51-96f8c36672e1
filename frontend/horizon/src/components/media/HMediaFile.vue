<script setup>
    import PrimeFileUpload from 'primevue/fileupload';
    import api from '../../../../backoffice/src/api/index.js';

    const props = defineProps({
        /**
         * Name of the request parameter to identify the files at backend.
         */
        name: {
            type: String,
            default: null,
        },
        /**
         * Remote url to upload the files.
         */
        url: {
            type: String,
            default: null,
        },
        /**
         * Defines the UI of the component, possible values are 'advanced' and 'basic'.
         */
        mode: {
            type: String,
            default: 'advanced',
            validator: value => ['advanced', 'basic'].includes(value),
        },
        /**
         * Used to select multiple files at once from file dialog.
         */
        multiple: {
            type: Boolean,
            default: false,
        },
        /**
         * Pattern to restrict the allowed file types such as 'image/*'.
         */
        accept: {
            type: String,
            default: null,
        },
        /**
         * Disables the upload functionality.
         */
        disabled: {
            type: Boolean,
            default: false,
        },
        /**
         * When enabled, upload begins automatically after selection is completed.
         */
        autoUpload: {
            type: Boolean,
            default: false,
        },
        /**
         * Message of the invalid fize type.
         */
        invalidFileTypeMessage: {
            type: String,
            default: '{0}: Invalid file type.',
        },
        /**
         * Maximum file size allowed in bytes.
         */
        maxFileSize: {
            type: Number,
            default: null,
        },
        /**
         * Message of the invalid file size.
         */
        invalidFileSizeMessage: {
            type: String,
            default: ': Invalid file size, file size should be smaller than {1.}',
        },
        /**
         * Maximum number of files that can be uploaded.
         */
        fileLimit: {
            type: Number,
            default: null,
        },
        /**
         * Message to display when number of files to be uploaded exceeeds the limit.
         */
        invalidFileLimitMessage: {
            type: String,
            default: 'Maximum number of files to be uploaded is {0.}',
        },
        /**
         * Cross-site Access-Control requests should be made using credentials such as
         * cookies, authorization headers or TLS client certificates.
         */
        withCredentials: {
            type: Boolean,
            default: false,
        },
        /**
         * Width of the image thumbnail in pixels.
         */
        previewWidth: {
            type: Number,
            default: 50,
        },
        /**
         * Label of the choose button. Defaults to PrimeVue Locale configuration.
         */
        chooseLabel: {
            type: String,
            default: null,
        },
        /**
         * Label of the upload button. Defaults to PrimeVue Locale configuration.
         */
        uploadLabel: {
            type: String,
            default: null,
        },
        /**
         * Label of the cancel button. Defaults to PrimeVue Locale configuration.
         */
        cancelLabel: {
            type: String,
            default: 'Cancel',
        },
        /**
         * Whether to use the default upload or a manual implementation defined in
         * uploadHandler callback. Defaults to PrimeVue Locale configuration.
         */
        customUpload: {
            type: Boolean,
            default: null,
        },
        /**
         * Whether to show the upload button.
         */
        showUploadButton: {
            type: Boolean,
            default: true,
        },
        /**
         * Whether to show the cancel button.
         */
        showCancelButton: {
            type: Boolean,
            default: true,
        },
        /**
         * Icon of the choose button.
         */
        chooseIcon: {
            type: String,
            default: null,
        },
        /**
         * Icon of the upload button.
         */
        uploadIcon: {
            type: String,
            default: null,
        },
        /**
         * Icon of the cancel button.
         */
        cancelIcon: {
            type: String,
            default: null,
        },
        /**
         * Used to pass all properties of the ButtonProps to the choose button inside
         * the component.
         */
        chooseButtonProps: {
            type: Object,
            default: null,
        },
        /**
         * Used to pass all properties of the ButtonProps to the upload button inside
         * the component.
         */
        uploadButtonProps: {
            type: Object,
            default: null,
        },
        /**
         * Used to pass all properties of the ButtonProps to the cancel button inside
         * the component.
         */
        cancelButtonProps: {
            type: Object,
            default: null,
        },
        /**
         * When enabled, it removes component related styles in the core.
         */
        unstyled: {
            type: Boolean,
            default: false,
        },
    });

    const emit = defineEmits(['select', 'upload', 'remove', 'error', 'clear']);

    const model = defineModel({type: Number, default: null});

    const handleSelect = async event => {
        debugger;
        if (event.files && event.files.length > 0) {
            try {
                const formData = new FormData();
                formData.append('file', event.files[0]);

                const response = await api.mediaFile.create(formData);
                model.value = response.data.id;
            } catch (error) {
                model.value = null;
                emit('error', error);
            }
        }
        emit('select', event);
    };

    defineOptions({
        inheritAttrs: false,
    });
</script>

<template>
    <PrimeFileUpload
        :name="name"
        :url="url"
        :mode="mode"
        :multiple="multiple"
        :accept="accept"
        :disabled="disabled"
        :auto="autoUpload"
        :max-file-size="maxFileSize"
        :invalid-file-size-message="invalidFileSizeMessage"
        :invalid-file-limit-message="invalidFileLimitMessage"
        :invalid-file-type-message="invalidFileTypeMessage"
        :file-limit="fileLimit"
        :with-credentials="withCredentials"
        :preview-width="previewWidth"
        :choose-label="chooseLabel"
        :upload-label="uploadLabel"
        :cancel-label="cancelLabel"
        :custom-upload="customUpload"
        :show-upload-button="showUploadButton"
        :show-cancel-button="showCancelButton"
        :choose-icon="chooseIcon"
        :upload-icon="uploadIcon"
        :cancel-icon="cancelIcon"
        :choose-button-props="chooseButtonProps"
        :upload-button-props="uploadButtonProps"
        :cancel-button-props="cancelButtonProps"
        :unstyled="unstyled"
        v-bind="$attrs"
        @select="handleSelect"
        @upload="$emit('upload', $event)"
        @remove="$emit('remove', $event)"
        @error="$emit('error', $event)"
        @clear="$emit('clear', $event)" />
</template>
