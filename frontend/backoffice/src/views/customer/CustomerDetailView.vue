<script setup>
    import {HPageTitle, HSkeleton, HSection, HButton} from '@horizon/components';
    import {useCustomerQuery} from '@/composables/customer.js';
    import {useRoute, useRouter} from 'vue-router';

    const route = useRoute();
    const router = useRouter();
    const customerId = computed(() => parseInt(route.params.id, 10));
    const {data, isLoading} = useCustomerQuery(customerId.value);

    const goToAddFinancialStatement = () => {
        router.push({name: 'add-financial-statement', params: {id: customerId.value}});
    };
</script>

<template>
    <template v-if="data && !isLoading">
        <HPageTitle :title="data.name" />
        <HSection>
            <div class="mb-4">
                <HButton semantic="primary" icon="ri:add-circle-line" @click="goToAddFinancialStatement">
                    Aggiungi Financial Statement
                </HButton>
            </div>
            {{ data }}
        </HSection>
    </template>
    <template v-else>
        <HSkeleton class="mb-1" />
        <HSkeleton class="mb-1" />
        <HSkeleton class="mb-1" />
        <HSkeleton class="mb-1" />
        <HSkeleton class="mb-1" />
        <HSkeleton class="mb-1" />
        <HSkeleton class="mb-1" />
    </template>
</template>
