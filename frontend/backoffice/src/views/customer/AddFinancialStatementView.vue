<script setup>
    import {HPageTitle, HSection, HButton} from '@horizon/components';
    import {ref} from 'vue';
    import {useRoute, useRouter} from 'vue-router';
    import api from '@/api';

    import HMediaFile from '@horizon/components/media/HMediaFile.vue';

    const route = useRoute();
    const router = useRouter();
    const customerId = computed(() => parseInt(route.params.id, 10));

    const pdfFile = ref(null);
    const xbrlFile = ref(null);
    const csvFile = ref(null);
    const loading = ref(false);

    const goBack = () => {
        router.push({name: 'customer-detail', params: {id: customerId.value}});
    };

    const submitFinancialStatement = async () => {
        loading.value = true;

        try {
            const formData = {
                pdf_file: pdfFile.value,
                xbrl_file: xbrlFile.value,
                csv_file: csvFile.value,
            };

            await api.financialStatement.create(formData);
            router.push({name: 'customer-detail', params: {id: customerId.value}});
        } catch {
            // Handle error
        } finally {
            loading.value = false;
        }
    };
</script>

<template>
    <HPageTitle title="Aggiungi Financial Statement" />

    <HSection>
        <div class="mb-6">
            <HButton semantic="secondary" icon="ri:arrow-left-line" @click="goBack">Torna al Cliente</HButton>
        </div>

        <div class="space-y-8">
            <div>
                <h3 class="text-lg font-medium mb-3">File PDF</h3>
                <HMediaFile
                    v-model="pdfFile"
                    name="pdf_file"
                    accept=".pdf"
                    mode="basic"
                    :auto-upload="false"
                    custom-upload
                    choose-label="Scegli file PDF" />
                <p class="text-sm text-gray-600 mt-2">Carica un file PDF del bilancio (opzionale)</p>
            </div>

            <div>
                <h3 class="text-lg font-medium mb-3">File XBRL</h3>
                <HMediaFile
                    v-model="xbrlFile"
                    name="xbrl_file"
                    accept=".xbrl,.xml"
                    mode="basic"
                    :auto-upload="false"
                    custom-upload
                    choose-label="Scegli file XBRL" />
                <p class="text-sm text-gray-600 mt-2">Carica un file XBRL del bilancio (opzionale)</p>
            </div>

            <div>
                <h3 class="text-lg font-medium mb-3">File CSV</h3>
                <HMediaFile
                    v-model="csvFile"
                    name="csv_file"
                    accept=".csv"
                    mode="basic"
                    :auto-upload="false"
                    custom-upload
                    choose-label="Scegli file CSV" />
                <p class="text-sm text-gray-600 mt-2">Carica un file CSV dei dati del bilancio (opzionale)</p>
            </div>

            <div class="flex justify-end gap-3 pt-6 border-t">
                <HButton semantic="secondary" @click="goBack">Annulla</HButton>
                <HButton semantic="primary" :loading="loading" @click="submitFinancialStatement">
                    Salva Financial Statement
                </HButton>
            </div>
        </div>
    </HSection>
</template>
