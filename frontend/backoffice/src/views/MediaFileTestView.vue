<script setup>
    import {HMediaFile} from '@horizon/components';
    import {ref} from 'vue';

    const singleFile = ref(null);
    const multipleFiles = ref(null);
    const advancedFiles = ref(null);
</script>

<template>
    <div class="p-4 space-y-6">
        <h1 class="text-2xl font-bold mb-4">HMediaFile Component Test</h1>

        <!-- Basic Mode (Default) -->
        <div class="border p-4 rounded">
            <h2 class="text-lg font-semibold mb-2">Basic Mode (Default)</h2>
            <HMediaFile v-model="singleFile" :auto-upload="false" />
        </div>

        <!-- Multiple Files -->
        <div class="border p-4 rounded">
            <h2 class="text-lg font-semibold mb-2">Multiple Files</h2>
            <HMediaFile
                v-model="multipleFiles"
                :auto-upload="false"
                :multiple="true"
                choose-label="Scegli File Multipli" />
        </div>

        <!-- Advanced Mode with Custom Props -->
        <div class="border p-4 rounded">
            <h2 class="text-lg font-semibold mb-2">Advanced Mode with Custom Props</h2>
            <HMediaFile
                v-model="advancedFiles"
                mode="advanced"
                :auto-upload="false"
                :multiple="true"
                accept="image/*"
                :file-limit="3"
                choose-label="Seleziona Immagini"
                upload-label="Carica"
                cancel-label="Annulla"
                invalid-file-limit-message="Massimo 3 file consentiti"
                invalid-file-type-message="Solo immagini sono consentite"
                url="/api/upload" />
        </div>

        <!-- Disabled State -->
        <div class="border p-4 rounded">
            <h2 class="text-lg font-semibold mb-2">Disabled State</h2>
            <HMediaFile :disabled="true" :auto-upload="false" choose-label="Upload Disabilitato" />
        </div>

        <!-- Auto Upload -->
        <div class="border p-4 rounded">
            <h2 class="text-lg font-semibold mb-2">Auto Upload</h2>
            <HMediaFile mode="advanced" :multiple="true" choose-label="Upload Automatico" url="/api/upload" />
        </div>

        <!-- Custom Icons -->
        <div class="border p-4 rounded">
            <h2 class="text-lg font-semibold mb-2">Custom Icons</h2>
            <HMediaFile
                mode="advanced"
                :auto-upload="false"
                choose-icon="pi pi-plus"
                upload-icon="pi pi-cloud-upload"
                cancel-icon="pi pi-times"
                choose-label="Aggiungi File"
                upload-label="Carica File"
                cancel-label="Cancella" />
        </div>
    </div>
</template>
