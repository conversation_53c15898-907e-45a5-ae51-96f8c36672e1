import {createRouter, CATCH_ALL_PATH, LOGIN_ROUTE_NAME} from '@common/router.js';
import {defineAsyncComponent} from 'vue';

// Define layouts in constants to avoid VueR<PERSON><PERSON> from reloading them
const AuthLayout = defineAsyncComponent(() => import('@/layouts/AuthLayout.vue'));
const FullPageLayout = defineAsyncComponent(() => import('@/layouts/FullPageLayout.vue'));

const routes = [
    {
        path: '/',
        component: () => import('@/views/DashboardView.vue'),
        name: 'dashboard',
    },
    {
        path: '/login',
        component: () => import('@/views/auth/LoginView.vue'),
        name: LOGIN_ROUTE_NAME,
        meta: {requiresAuth: false, layout: AuthLayout},
    },
    {
        path: '/customer',
        component: () => import('@/views/customer/CustomerListView.vue'),
        name: 'customer-list',
    },
    {
        path: '/customer/:id',
        component: () => import('@/views/customer/CustomerDetailView.vue'),
        name: 'customer-detail',
    },
    {
        path: '/customer/:id/add-financial-statement',
        component: () => import('@/views/customer/AddFinancialStatementView.vue'),
        name: 'add-financial-statement',
    },
    {
        path: '/media-test',
        component: () => import('@/views/MediaFileTestView.vue'),
        name: 'media-test',
    },
    {
        path: CATCH_ALL_PATH,
        component: () => import('@/views/errors/NotFoundView.vue'),
        name: 'not-found',
        meta: {
            requiresAuth: false,
            layout: FullPageLayout,
        },
    },
];

const router = createRouter({
    routes,
});

function pushRouteOnAuthentication(isAuthenticated) {
    if (isAuthenticated) {
        router.push({name: 'dashboard'});
    } else {
        router.push({name: LOGIN_ROUTE_NAME});
    }
}

export default router;
export {pushRouteOnAuthentication};
