# System
.DS_Store

# IDE
.vscode
.idea

# Docker
docker/shared/*
!docker/shared/.gitkeep

# Backend
.venv
backend/.ruff_cache
backend/django/media/
backend/django/project/settings/secrets/*
!backend/django/project/settings/secrets/__init__.py
!backend/django/project/settings/secrets/*.gpg

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
*.log
.pytest_cache/
*.egg-info/
.eggs/
.coverage
.coverage.*
htmlcov/
.tox/
.nox/

# Frontend
frontend/node_modules

# AI agents
/.junie/
/.claude/
/CLAUDE.md
