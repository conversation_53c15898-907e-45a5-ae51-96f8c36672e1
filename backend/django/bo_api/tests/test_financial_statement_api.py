import pytest
from django.core.files.uploadedfile import SimpleUploadedFile
from django.urls import reverse
from rest_framework.test import APIClient

from costing.factories.bo_user import BOUser<PERSON>lainFactory
from costing.models import FinancialStatement, MediaFile


@pytest.fixture
def bo_user(db):
    bo_user = BOUserPlainFactory(email="<EMAIL>")
    bo_user.set_password("test")
    bo_user.save(update_fields=["password_hash"])
    return bo_user


@pytest.fixture
def authenticated_client(bo_user):
    client = APIClient()
    client.force_authenticate(user=bo_user)
    return client


@pytest.fixture
def sample_pdf_file():
    content = b"This is a test PDF file content."
    return SimpleUploadedFile("test.pdf", content, content_type="application/pdf")


@pytest.fixture
def sample_xbrl_file():
    content = b"<xbrl>This is a test XBRL file content.</xbrl>"
    return SimpleUploadedFile("test.xbrl", content, content_type="application/xml")


@pytest.fixture
def sample_csv_file():
    content = b"header1,header2\nvalue1,value2"
    return SimpleUploadedFile("test.csv", content, content_type="text/csv")


@pytest.fixture
def media_files(sample_pdf_file, sample_xbrl_file, sample_csv_file):
    pdf_media = MediaFile.objects.create(file=sample_pdf_file)
    xbrl_media = MediaFile.objects.create(file=sample_xbrl_file)
    csv_media = MediaFile.objects.create(file=sample_csv_file)
    return {
        "pdf": pdf_media,
        "xbrl": xbrl_media,
        "csv": csv_media,
    }


@pytest.mark.django_db
def test_financial_statement_create_api(authenticated_client, media_files):
    """Test creating a FinancialStatement via API."""
    url = reverse("financial_statements-list")
    data = {
        "pdf_file": media_files["pdf"].id,
        "xbrl_file": media_files["xbrl"].id,
        "csv_file": media_files["csv"].id,
    }

    response = authenticated_client.post(url, data, format="json")

    assert response.status_code == 201
    assert FinancialStatement.objects.count() == 1

    financial_statement = FinancialStatement.objects.first()
    response_data = response.json()

    assert response_data["id"] == financial_statement.id
    assert response_data["pdf_file"] == media_files["pdf"].id
    assert response_data["xbrl_file"] == media_files["xbrl"].id
    assert response_data["csv_file"] == media_files["csv"].id


@pytest.mark.django_db
def test_financial_statement_create_api_partial(authenticated_client, media_files):
    """Test creating a FinancialStatement with only some files."""
    url = reverse("financial_statements-list")
    data = {
        "pdf_file": media_files["pdf"].id,
        # xbrl_file and csv_file are optional
    }

    response = authenticated_client.post(url, data, format="json")

    assert response.status_code == 201
    assert FinancialStatement.objects.count() == 1

    response_data = response.json()

    assert response_data["pdf_file"] == media_files["pdf"].id
    assert response_data["xbrl_file"] is None
    assert response_data["csv_file"] is None


@pytest.mark.django_db
def test_financial_statement_retrieve_api(authenticated_client, media_files):
    """Test retrieving a FinancialStatement via API."""
    financial_statement = FinancialStatement.objects.create(
        pdf_file=media_files["pdf"],
        xbrl_file=media_files["xbrl"],
        csv_file=media_files["csv"],
    )

    url = reverse("financial_statements-detail", kwargs={"pk": financial_statement.pk})
    response = authenticated_client.get(url)

    assert response.status_code == 200

    response_data = response.json()
    assert response_data["id"] == financial_statement.id
    assert response_data["pdf_file"] == media_files["pdf"].id
    assert response_data["xbrl_file"] == media_files["xbrl"].id
    assert response_data["csv_file"] == media_files["csv"].id


@pytest.mark.django_db
def test_financial_statement_list_api(authenticated_client, media_files):
    """Test listing FinancialStatements via API."""
    FinancialStatement.objects.create(
        pdf_file=media_files["pdf"],
        xbrl_file=media_files["xbrl"],
    )
    FinancialStatement.objects.create(
        csv_file=media_files["csv"],
    )

    url = reverse("financial_statements-list")
    response = authenticated_client.get(url)

    assert response.status_code == 200

    response_data = response.json()
    # Check if response is paginated or direct list
    if isinstance(response_data, dict) and "results" in response_data:
        assert len(response_data["results"]) == 2
    else:
        assert len(response_data) == 2


@pytest.mark.django_db
def test_financial_statement_update_api(authenticated_client, media_files):
    """Test updating a FinancialStatement via API."""
    financial_statement = FinancialStatement.objects.create(
        pdf_file=media_files["pdf"],
    )

    url = reverse("financial_statements-detail", kwargs={"pk": financial_statement.pk})
    data = {
        "pdf_file": media_files["pdf"].id,
        "xbrl_file": media_files["xbrl"].id,
        "csv_file": media_files["csv"].id,
    }

    response = authenticated_client.patch(url, data, format="json")

    assert response.status_code == 200

    financial_statement.refresh_from_db()
    assert financial_statement.pdf_file == media_files["pdf"]
    assert financial_statement.xbrl_file == media_files["xbrl"]
    assert financial_statement.csv_file == media_files["csv"]


@pytest.mark.django_db
def test_financial_statement_delete_api(authenticated_client, media_files):
    """Test deleting a FinancialStatement via API."""
    financial_statement = FinancialStatement.objects.create(
        pdf_file=media_files["pdf"],
    )

    url = reverse("financial_statements-detail", kwargs={"pk": financial_statement.pk})
    response = authenticated_client.delete(url)

    assert response.status_code == 204
    assert FinancialStatement.objects.count() == 0


@pytest.mark.django_db
def test_financial_statement_api_unauthenticated():
    """Test that unauthenticated requests are rejected."""
    client = APIClient()

    # Test create
    url = reverse("financial_statements-list")
    response = client.post(url, {})
    assert response.status_code == 403

    # Test retrieve
    url = reverse("financial_statements-detail", kwargs={"pk": 1})
    response = client.get(url)
    assert response.status_code == 403

    # Test delete
    response = client.delete(url)
    assert response.status_code == 403
