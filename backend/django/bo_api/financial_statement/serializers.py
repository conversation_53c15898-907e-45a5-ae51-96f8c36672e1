from rest_framework import serializers

from costing.models import FinancialStatement


class FinancialStatementSerializer(serializers.ModelSerializer):
    class Meta:
        model = FinancialStatement
        fields = [
            "id",
            "pdf_file",
            "xbrl_file",
            "csv_file",
            "created_at",
            "modified_at",
        ]
        read_only_fields = [
            "created_at",
            "modified_at",
        ]
