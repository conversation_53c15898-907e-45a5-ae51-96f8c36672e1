from rest_framework import viewsets

from common.drf import BackofficeViewSetMixin
from costing.models import FinancialStatement

from .serializers import FinancialStatementSerializer


class FinancialStatementViewSet(BackofficeViewSetMixin, viewsets.ModelViewSet):
    serializer_class = FinancialStatementSerializer
    ordering_fields = ["created_at"]

    def get_queryset(self):
        return FinancialStatement.objects.all().select_related(
            "pdf_file", "xbrl_file", "csv_file"
        )
