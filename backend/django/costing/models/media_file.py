import uuid
from pathlib import Path

from django.db import models
from django.db.models import (
    Case,
    ForeignKey,
    ManyToManyField,
    OneToOneField,
    Q,
    Value,
    When,
)
from django.db.models.query import QuerySet
from django.utils.text import get_valid_filename

from common.db import BaseModel
from common.files import get_file_mimetype


def file_path(instance, filename):
    uid = str(uuid.uuid4())
    return f"media_files/{uid}/{get_valid_filename(filename)}"


class MediaFileQuerySet(QuerySet):
    def annotate_in_use(self):
        """
        Annotate queryset with in_use field indicating if MediaFile
        is referenced by other models.
        Uses MediaFile._meta.related_objects to dynamically check all relationships.
        """
        # Get all related objects dynamically from MediaFile model
        related_objects = self.model._meta.related_objects  # noqa: SLF001

        # Build Q objects for all related fields
        q_conditions = []
        for related in related_objects:
            related_name = related.get_accessor_name()
            q_conditions.append(Q(**{f"{related_name}__isnull": False}))

        # Combine all conditions with OR
        if q_conditions:
            combined_q = q_conditions[0]
            for q in q_conditions[1:]:
                combined_q |= q
        else:
            # If no related objects, always False
            combined_q = Q(pk__isnull=True)  # Always False condition

        return self.annotate(
            in_use=Case(
                When(combined_q, then=Value(True)),
                default=Value(False),
                output_field=models.BooleanField(),
            )
        )


class MediaFileManager(models.Manager.from_queryset(MediaFileQuerySet)):
    pass


class MediaFile(BaseModel):
    file = models.FileField(verbose_name="File", max_length=1024, upload_to=file_path)
    filename = models.CharField(verbose_name="Nome file", max_length=255, blank=True)
    mime_type = models.CharField(verbose_name="Tipo MIME", max_length=255, blank=True)
    size = models.PositiveIntegerField(verbose_name="Dimensione (bytes)", default=0)

    objects = MediaFileManager()

    class Meta:
        verbose_name = "Media file"
        verbose_name_plural = "Media files"

    def __str__(self):
        return self.filename or self.file.name.split("/")[-1]

    def save(self, *args, **kwargs):
        if self.file:
            # Set filename if not provided
            if not self.filename:
                self.filename = Path(self.file.name).name

            # Set file size if available
            if hasattr(self.file, "size"):
                self.size = self.file.size

            # Set mime type if not provided
            if not self.mime_type:
                try:
                    # Use the get_file_mimetype function for MIME type detection
                    self.mime_type = get_file_mimetype(self.file)
                except (OSError, AttributeError, ValueError):
                    # Fallback to a generic mime type if detection fails
                    self.mime_type = "application/octet-stream"

        super().save(*args, **kwargs)

    def get_related_objects_details(self) -> list:
        objects = []
        for related in self._meta.related_objects:
            related_name = related.get_accessor_name()
            field = related.field
            related_manager = getattr(self, related_name, None)
            if related_manager:
                is_m2m = isinstance(field, ManyToManyField) and related_manager.exists()

                if isinstance(field, (ForeignKey, OneToOneField)) or is_m2m:
                    related_pk = list(related_manager.values_list("id", flat=True))
                    if related_pk:
                        objects.append(
                            {
                                "related_name": related_name,
                                "related_pk": related_pk,
                            }
                        )
        return objects
